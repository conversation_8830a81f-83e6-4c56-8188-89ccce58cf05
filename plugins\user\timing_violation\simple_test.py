#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def normalize_check_info(check_info: str) -> str:
    """标准化检查信息，用于模糊匹配"""
    try:
        # 查找括号内容
        start_idx = check_info.find('(')
        end_idx = check_info.rfind(')')
        
        if start_idx == -1 or end_idx == -1 or start_idx >= end_idx:
            # 如果没有找到括号，返回原始信息
            return check_info
        
        # 提取括号前的部分和括号内的部分
        prefix = check_info[:start_idx + 1]  # 包含开括号
        bracket_content = check_info[start_idx + 1:end_idx]
        
        # 按逗号分割括号内容
        parts = bracket_content.split(',')
        
        if len(parts) < 3:
            # 如果分割后少于3部分，返回原始信息
            return check_info
        
        normalized_parts = []
        
        # 处理第一部分：移除冒号后的时间信息
        part1 = parts[0].strip()
        colon_idx = part1.find(':')
        if colon_idx != -1:
            part1 = part1[:colon_idx].strip()
        normalized_parts.append(part1)
        
        # 处理第二部分：移除冒号后的时间信息
        part2 = parts[1].strip()
        colon_idx = part2.find(':')
        if colon_idx != -1:
            part2 = part2[:colon_idx].strip()
        normalized_parts.append(part2)
        
        # 第三部分忽略，不添加到标准化结果中
        
        # 重新组装标准化的检查信息
        normalized_bracket_content = ', '.join(normalized_parts)
        normalized_check_info = prefix + normalized_bracket_content + ')'
        
        return normalized_check_info
        
    except Exception as e:
        print(f"标准化检查信息失败: {str(e)}, 原始信息: {check_info}")
        return check_info

def main():
    print("时序违例插件模糊匹配功能测试")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            'input': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )',
            'expected': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK, negedge SE)'
        },
        {
            'input': 'setuphold<hold>( posedge clk:1234567 PS, data:9876543 NS, timing_info )',
            'expected': 'setuphold<hold>( posedge clk, data)'
        },
        {
            'input': 'width( signal:12345 FS, min_width, extra_info )',
            'expected': 'width( signal, min_width)'
        }
    ]
    
    print("测试标准化功能:")
    for i, case in enumerate(test_cases, 1):
        result = normalize_check_info(case['input'])
        status = "✓" if result == case['expected'] else "✗"
        print(f"{i}. {status}")
        print(f"   输入: {case['input']}")
        print(f"   期望: {case['expected']}")
        print(f"   实际: {result}")
        if result != case['expected']:
            print(f"   ❌ 不匹配!")
        print()
    
    # 测试模糊匹配
    print("测试模糊匹配:")
    historical_check = "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:1111111 FS, negedge SE:2222222 FS,0.1234 : 123400FS, -0.0567 : -56700 FS )"
    historical_normalized = normalize_check_info(historical_check)
    
    new_checks = [
        "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )",
        "setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:9999999 FS, negedge SE:8888888 FS,0.2000 : 200000FS, -0.0100 : -10000 FS )"
    ]
    
    print(f"历史模式标准化: {historical_normalized}")
    print()
    
    for i, new_check in enumerate(new_checks, 1):
        new_normalized = normalize_check_info(new_check)
        is_match = historical_normalized == new_normalized
        
        print(f"测试用例 {i}: {'✓ 匹配' if is_match else '✗ 不匹配'}")
        print(f"   新检查标准化: {new_normalized}")
        print()

if __name__ == "__main__":
    main()
