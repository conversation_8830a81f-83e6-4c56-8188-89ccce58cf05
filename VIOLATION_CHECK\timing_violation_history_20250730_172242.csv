﻿层级路径,检查信息,确认人,确认结果,确认理由,使用次数,最后使用时间
tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[27],"setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )",aaa,通过,bbb,1,2025-07-30 17:22:26
tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[28],"setup( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )",aaa,通过,bbb,1,2025-07-30 17:22:26
tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[26],"setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:6869100 FS, negedge SE:6735500 FS,0.1621 : 162100FS, -0.0261 : -26100 FS )",aaa,通过,bbb,1,2025-07-30 17:22:25
tb.pipeline.memory_ctrl.mmu[29].cache_unit[12],Removal time violation on signal valid,aaa,通过,bbb,10,2025-07-30 17:21:54
tb.debug_unit.pcie_if.interrupt_ctrl.branch_pred[5].interrupt_ctrl,Recovery time violation on signal done[4],aaa,通过,ccc,10,2025-07-30 17:21:54
tb.decode_unit[8].writeback_unit.cache_unit.cache_unit[18].decode_unit.interrupt_ctrl,Skew violation on signal rst_n,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.debug_unit.debug_unit.memory_ctrl.cpu_top[10].cache_unit[23].interrupt_ctrl,Recovery time violation on signal wr_en[51],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.interrupt_ctrl.debug_unit.decode_unit.debug_unit.pcie_if[13],Pulse width violation on signal clk,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pipeline.fetch_unit.pipeline[23].cpu_top.branch_pred,Period violation on signal status,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pipeline.cpu_top[5].memory_ctrl[26].writeback_unit.tlb,Skew violation on signal error,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.branch_pred.mmu[24].branch_pred.execute_unit.interrupt_ctrl,Setup time violation on signal data[2],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit[25].tlb[15].cache_unit[0],Hold time violation on signal cs,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.writeback_unit.branch_pred.decode_unit[21].pipeline,Pulse width violation on signal mode,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cache_unit.writeback_unit.execute_unit,Recovery time violation on signal intr[51],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cpu_top.tlb.tlb.mmu[26].memory_ctrl[14].branch_pred,Pulse width violation on signal enable[9],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pcie_if.memory_ctrl.tlb.fetch_unit.debug_unit[1],Removal time violation on signal error[60],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.interrupt_ctrl[18].debug_unit.interrupt_ctrl.pcie_if[9].mmu,Pulse width violation on signal rd_en,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.interrupt_ctrl.interrupt_ctrl.debug_unit[0].pipeline,Period violation on signal mode,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.mmu.cache_unit[16].memory_ctrl,Setup time violation on signal ack,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cache_unit.tlb.execute_unit[24].interrupt_ctrl.fetch_unit.decode_unit,Recovery time violation on signal error,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.interrupt_ctrl.pipeline.execute_unit.writeback_unit.tlb.debug_unit,Hold time violation on signal valid,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pipeline.debug_unit[15].decode_unit,Recovery time violation on signal busy[11],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cpu_top[2].execute_unit.interrupt_ctrl.fetch_unit[8].memory_ctrl.mmu,Setup time violation on signal idle,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.mmu.ddr_ctrl[20].mmu.ddr_ctrl.branch_pred.writeback_unit,Recovery time violation on signal clk,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.debug_unit.decode_unit.cache_unit[3].execute_unit[26],Period violation on signal grant,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cpu_top.mmu.pcie_if[9].pcie_if.cpu_top.interrupt_ctrl,Removal time violation on signal ack,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pcie_if.branch_pred.decode_unit.pipeline.interrupt_ctrl.branch_pred,Period violation on signal grant,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.execute_unit.cache_unit.fetch_unit.writeback_unit.memory_ctrl[2],Period violation on signal ack,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.interrupt_ctrl[5].decode_unit.memory_ctrl.cpu_top[23].writeback_unit.decode_unit,Hold time violation on signal valid[58],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.branch_pred.writeback_unit.pcie_if.decode_unit[7],Hold time violation on signal valid,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.debug_unit[5].debug_unit[7].ddr_ctrl,Skew violation on signal enable,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb.cache_unit.debug_unit.memory_ctrl,Removal time violation on signal error[45],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pipeline.decode_unit.fetch_unit.mmu[24].execute_unit.branch_pred[25],Recovery time violation on signal grant[32],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cpu_top[4].execute_unit[2].mmu.writeback_unit.debug_unit,Pulse width violation on signal data[19],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb.ddr_ctrl.cache_unit,Setup time violation on signal rst_n[11],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.interrupt_ctrl.tlb[5].tlb[13].fetch_unit[12].pipeline[12],Period violation on signal data,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit.writeback_unit.pipeline.cpu_top[1].mmu[24].pipeline,Hold time violation on signal mode,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.memory_ctrl.cache_unit.pipeline,Removal time violation on signal valid,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit.memory_ctrl.fetch_unit.interrupt_ctrl,Pulse width violation on signal enable,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit[28].mmu.execute_unit[15].writeback_unit.debug_unit.interrupt_ctrl,Period violation on signal clk[50],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit.cache_unit[13].mmu.pipeline[4],Period violation on signal busy,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.mmu.branch_pred[7].cpu_top[0].branch_pred[20].cache_unit,Skew violation on signal mode[31],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.debug_unit[31].cache_unit.memory_ctrl.tlb,Setup time violation on signal rst_n[6],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit[21].mmu.branch_pred.interrupt_ctrl.debug_unit,Removal time violation on signal valid[19],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit.writeback_unit[19].pipeline[6].cpu_top[10].decode_unit.ddr_ctrl,Period violation on signal addr,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cache_unit.pcie_if.fetch_unit.execute_unit.ddr_ctrl,Pulse width violation on signal intr,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.decode_unit.cache_unit.cache_unit.pcie_if.cpu_top[18],Removal time violation on signal intr[36],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.branch_pred.pcie_if.mmu[22].debug_unit,Recovery time violation on signal mode[22],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.pipeline[29].branch_pred.memory_ctrl[13],Period violation on signal mode,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.ddr_ctrl.pcie_if[22].pipeline[18],Setup time violation on signal addr,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.branch_pred.mmu.interrupt_ctrl.memory_ctrl[29],Removal time violation on signal ready,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.fetch_unit.branch_pred[11].execute_unit.interrupt_ctrl.pcie_if.pipeline[19],Hold time violation on signal error[17],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb[13].memory_ctrl.pipeline.mmu.decode_unit.memory_ctrl,Period violation on signal intr[13],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.writeback_unit.cpu_top.ddr_ctrl,Period violation on signal rd_en[20],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.memory_ctrl[14].writeback_unit[1].memory_ctrl[20],Skew violation on signal ready[2],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb.interrupt_ctrl[6].memory_ctrl[1].memory_ctrl[5],Hold time violation on signal clk[50],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.decode_unit.fetch_unit.pipeline.tlb[16].decode_unit,Period violation on signal mode[33],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.debug_unit.pcie_if.memory_ctrl.cache_unit.execute_unit.pcie_if,Hold time violation on signal valid,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.cpu_top.tlb[28].fetch_unit[17],Period violation on signal clk[3],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb[17].ddr_ctrl[2].debug_unit.ddr_ctrl.ddr_ctrl,Removal time violation on signal rd_en[61],aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.mmu.ddr_ctrl.ddr_ctrl,Setup time violation on signal rst_n,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb[20].interrupt_ctrl[15].pcie_if.cache_unit.decode_unit.interrupt_ctrl,Setup time violation on signal intr,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.branch_pred[3].memory_ctrl.decode_unit[7].mmu,Hold time violation on signal error,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.tlb[12].debug_unit[1].interrupt_ctrl[12].ddr_ctrl,Setup time violation on signal error,aaa,通过,bbbcccddd,9,2025-07-30 17:21:54
tb.writeback_unit.ddr_ctrl.decode_unit[17].memory_ctrl,Recovery time violation on signal data,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.memory_ctrl.execute_unit[19].debug_unit,Hold time violation on signal req,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.ddr_ctrl.interrupt_ctrl.fetch_unit.mmu[3],Removal time violation on signal idle,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.fetch_unit[6].pcie_if.execute_unit[9],Period violation on signal ready,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.tlb.interrupt_ctrl.execute_unit,Recovery time violation on signal idle[17],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.tlb.writeback_unit.debug_unit.ddr_ctrl.fetch_unit,Hold time violation on signal grant,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.interrupt_ctrl[29].pcie_if.execute_unit.memory_ctrl.cache_unit,Hold time violation on signal mode,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.fetch_unit.writeback_unit[26].memory_ctrl.mmu,Skew violation on signal addr[38],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.branch_pred.memory_ctrl.interrupt_ctrl,Removal time violation on signal grant[43],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.debug_unit.execute_unit.branch_pred.branch_pred.pipeline.decode_unit,Period violation on signal busy,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.interrupt_ctrl[15].pipeline.decode_unit.tlb.mmu[15],Period violation on signal error[22],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.memory_ctrl[6].mmu[30].mmu.debug_unit[9].interrupt_ctrl,Pulse width violation on signal ack[23],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cache_unit.decode_unit.ddr_ctrl.interrupt_ctrl.tlb,Period violation on signal intr,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.fetch_unit[29].fetch_unit.decode_unit[1].decode_unit,Skew violation on signal grant,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.debug_unit.writeback_unit.tlb.mmu[17],Period violation on signal rst_n,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.pcie_if.debug_unit[27].execute_unit.branch_pred[15].execute_unit.cache_unit,Pulse width violation on signal error,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.mmu[8].memory_ctrl.branch_pred,Recovery time violation on signal data[46],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.tlb[30].debug_unit.cpu_top[7].fetch_unit.cpu_top[20].tlb[14],Recovery time violation on signal mode,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.mmu.cache_unit.fetch_unit.ddr_ctrl[3].ddr_ctrl[9],Skew violation on signal error[34],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.interrupt_ctrl.writeback_unit.pipeline[6],Setup time violation on signal clk,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.tlb[20].execute_unit,Pulse width violation on signal ready,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cache_unit.execute_unit.pipeline.cpu_top.cache_unit,Period violation on signal mode,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.ddr_ctrl.pipeline.fetch_unit.mmu.memory_ctrl[25].cache_unit,Recovery time violation on signal done[52],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.writeback_unit.interrupt_ctrl[19].ddr_ctrl,Hold time violation on signal error[2],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.pcie_if[24].mmu.debug_unit,Skew violation on signal mode,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.mmu.mmu.tlb,Removal time violation on signal addr[63],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.writeback_unit.cpu_top.execute_unit.decode_unit.decode_unit.branch_pred,Recovery time violation on signal ack[45],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.mmu.interrupt_ctrl.fetch_unit.pipeline[18].pcie_if,Removal time violation on signal addr[13],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.decode_unit.cpu_top.interrupt_ctrl.pcie_if.mmu,Recovery time violation on signal rd_en,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.ddr_ctrl[14].debug_unit[29].debug_unit.fetch_unit.decode_unit.memory_ctrl[25],Hold time violation on signal rst_n[20],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.debug_unit[30].pcie_if.execute_unit,Skew violation on signal rst_n[24],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.tlb[26].cpu_top.branch_pred[26],Removal time violation on signal enable[60],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.branch_pred[20].cache_unit.cache_unit,Pulse width violation on signal addr,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.decode_unit.ddr_ctrl[0].tlb.interrupt_ctrl.writeback_unit,Setup time violation on signal ready,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.tlb[1].pipeline[16].pcie_if.decode_unit[24],Period violation on signal valid[57],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.pipeline.pipeline.interrupt_ctrl.writeback_unit[25],Setup time violation on signal rd_en[6],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.execute_unit.memory_ctrl.writeback_unit,Period violation on signal busy,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.writeback_unit.cache_unit.fetch_unit.tlb.debug_unit[4],Period violation on signal ready[62],aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.cpu_top.fetch_unit[13].interrupt_ctrl.pcie_if[25],Period violation on signal clk,aaa,通过,bbbcccddd,3,2025-07-30 17:21:54
tb.interrupt_ctrl.branch_pred.cpu_top.cache_unit.tlb.writeback_unit,Skew violation on signal idle[19],aaa,通过,test,19,2025-07-30 09:57:19
tb.branch_pred[7].interrupt_ctrl.cpu_top.tlb.writeback_unit.tlb,Period violation on signal grant[43],aaa,通过,test,19,2025-07-30 09:57:19
tb.tlb[16].mmu.tlb.mmu[12],Period violation on signal req,aaa,通过,test,19,2025-07-30 09:57:19
tb.writeback_unit[27].pcie_if.debug_unit.ddr_ctrl,Hold time violation on signal error[1],aaa,通过,test,19,2025-07-30 09:57:19
tb.fetch_unit.pcie_if.memory_ctrl.writeback_unit,Setup time violation on signal data,aaa,通过,test,19,2025-07-30 09:57:19
tb.pipeline[9].branch_pred.tlb.pcie_if.execute_unit,Recovery time violation on signal ack,aaa,通过,test,19,2025-07-30 09:57:19
tb.ddr_ctrl.cache_unit.cache_unit,Skew violation on signal enable,aaa,通过,test,19,2025-07-30 09:57:19
tb.fetch_unit.cache_unit.cache_unit.tlb.pipeline.execute_unit,Hold time violation on signal clk,aaa,通过,test,19,2025-07-30 09:57:19
tb.pipeline.fetch_unit.cpu_top[7].branch_pred.fetch_unit[25],Period violation on signal addr[58],aaa,通过,test,19,2025-07-30 09:57:19
tb.mmu.execute_unit.debug_unit,Setup time violation on signal clk[0],aaa,通过,test,19,2025-07-30 09:57:19
tb.execute_unit.ddr_ctrl.tlb.mmu.cache_unit.branch_pred[17],Hold time violation on signal addr[23],aaa,通过,test,19,2025-07-30 09:57:19
tb.branch_pred.pcie_if[22].tlb.cache_unit.tlb[19].execute_unit,Pulse width violation on signal data,aaa,通过,test,19,2025-07-30 09:57:19
tb.pipeline.debug_unit[6].cache_unit.cache_unit.branch_pred[10],Removal time violation on signal done[52],aaa,通过,test,19,2025-07-30 09:57:19
tb.branch_pred[24].pipeline.tlb.ddr_ctrl[2].branch_pred,Pulse width violation on signal grant[19],aaa,通过,test,19,2025-07-30 09:57:19
tb.pipeline[19].pcie_if[4].cache_unit.decode_unit[25].debug_unit[6],Pulse width violation on signal wr_en,aaa,通过,test,19,2025-07-30 09:57:19
tb.debug_unit.fetch_unit.pipeline[21],Setup time violation on signal busy[33],aaa,通过,test,19,2025-07-30 09:57:19
tb.ddr_ctrl.writeback_unit[6].fetch_unit[15].writeback_unit[17],Pulse width violation on signal wr_en[55],aaa,通过,test,19,2025-07-30 09:57:19
tb.pipeline.branch_pred.tlb.memory_ctrl,Recovery time violation on signal valid[21],aaa,通过,test,19,2025-07-30 09:57:19
tb.execute_unit.memory_ctrl[15].debug_unit.pipeline.mmu[13],Skew violation on signal rst_n[24],aaa,通过,test,19,2025-07-30 09:57:19
tb.decode_unit[18].pipeline.cache_unit[15],Period violation on signal busy[47],aaa,通过,test,19,2025-07-30 09:57:19
tb.interrupt_ctrl.execute_unit[20].ddr_ctrl[27].memory_ctrl.tlb,Period violation on signal error[24],aaa,通过,test,19,2025-07-30 09:57:19
tb.cpu_top[30].pipeline.pcie_if.mmu.cache_unit[21].pipeline,Period violation on signal wr_en[55],aaa,通过,test,19,2025-07-30 09:57:19
tb.fetch_unit.writeback_unit.execute_unit.ddr_ctrl.debug_unit[13].cache_unit,Pulse width violation on signal rst_n,aaa,通过,test,19,2025-07-30 09:57:19
tb.interrupt_ctrl.tlb.debug_unit.pipeline.writeback_unit,Setup time violation on signal ready[11],aaa,通过,test,19,2025-07-30 09:57:19
tb.interrupt_ctrl[17].mmu.cpu_top[24].tlb.memory_ctrl,Recovery time violation on signal req,aaa,通过,test,19,2025-07-30 09:57:19
tb.writeback_unit.cache_unit.branch_pred.ddr_ctrl.branch_pred[5].decode_unit[6],Recovery time violation on signal mode[26],aaa,通过,test,19,2025-07-30 09:57:19
tb.execute_unit[14].ddr_ctrl.cache_unit.branch_pred.cache_unit.debug_unit,Hold time violation on signal ack,aaa,通过,test,19,2025-07-30 09:57:19
tb.debug_unit[13].ddr_ctrl.mmu,Removal time violation on signal idle[27],aaa,通过,test,19,2025-07-30 09:57:19
tb.memory_ctrl.debug_unit.interrupt_ctrl,Period violation on signal data[53],aaa,通过,test,19,2025-07-30 09:57:19
tb.interrupt_ctrl.mmu.branch_pred[15].cache_unit[16].mmu,Removal time violation on signal wr_en,aaa,通过,test,19,2025-07-30 09:57:19
tb.ddr_ctrl.cpu_top.fetch_unit.writeback_unit,Skew violation on signal valid[20],aaa,通过,test,19,2025-07-30 09:57:19
tb.memory_ctrl.fetch_unit.writeback_unit.cache_unit.tlb.ddr_ctrl,Hold time violation on signal idle,aaa,通过,test,19,2025-07-30 09:57:19
tb.pipeline[22].fetch_unit.decode_unit.execute_unit,Setup time violation on signal error,aaa,通过,test,19,2025-07-30 09:57:19
tb.debug_unit.writeback_unit.pipeline,Setup time violation on signal grant[2],aaa,通过,test,19,2025-07-30 09:57:19
tb.decode_unit[20].cpu_top.mmu.debug_unit,Pulse width violation on signal intr[24],aaa,通过,test,19,2025-07-30 09:57:19
tb.tlb.branch_pred.mmu[0].memory_ctrl,Skew violation on signal valid,aaa,通过,test,19,2025-07-30 09:57:19
tb.tlb[31].decode_unit.cpu_top.cache_unit.debug_unit.writeback_unit,Pulse width violation on signal intr[56],aaa,通过,test,19,2025-07-30 09:57:19
tb.decode_unit.fetch_unit[13].cache_unit.pipeline.debug_unit,Skew violation on signal idle[52],aaa,通过,test,19,2025-07-30 09:57:19
tb.decode_unit.tlb.debug_unit.branch_pred,Period violation on signal data[30],aaa,通过,test,19,2025-07-30 09:57:19
tb.fetch_unit.fetch_unit.decode_unit.cpu_top.memory_ctrl[16],Period violation on signal req[32],ss,通过,bb,11,2025-07-30 09:57:19
tb.pipeline.fetch_unit.execute_unit.pipeline,Skew violation on signal ack,aaa,通过,bbb,11,2025-07-30 09:57:19
tb.pcie_if.execute_unit.cpu_top[11],Hold time violation on signal done,aaa,通过,bbb,7,2025-07-30 09:57:19
