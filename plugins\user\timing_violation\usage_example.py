#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
时序违例插件模糊匹配功能使用示例

这个示例展示了如何在实际应用中使用模糊匹配功能来自动确认时序违例。
"""

import os
import tempfile
import sys

# 添加插件路径
plugin_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, plugin_dir)

def simulate_usage_scenario():
    """模拟实际使用场景"""
    
    print("=" * 80)
    print("时序违例插件模糊匹配功能使用示例")
    print("=" * 80)
    print()
    
    # 模拟场景：工程师第一次遇到某种类型的时序违例
    print("场景1：首次遇到时序违例，手动确认并保存模式")
    print("-" * 50)
    
    # 第一次遇到的违例
    first_violation = {
        'hier': 'top.cpu.core.reg_file.data_reg[0]',
        'check': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:1234567 FS, negedge SE:7654321 FS,0.1500 : 150000FS, -0.0200 : -20000 FS )',
        'confirmer': '张工程师',
        'result': '忽略',
        'reason': '复位期间时序违例，可以忽略'
    }
    
    print(f"层级路径: {first_violation['hier']}")
    print(f"检查信息: {first_violation['check']}")
    print(f"确认结果: {first_violation['result']}")
    print(f"确认理由: {first_violation['reason']}")
    print("→ 工程师手动确认并保存为历史模式")
    print()
    
    # 模拟场景：后续遇到相同类型但时间戳不同的违例
    print("场景2：后续遇到相同类型但时间戳不同的违例")
    print("-" * 50)
    
    subsequent_violations = [
        {
            'hier': 'top.cpu.core.reg_file.data_reg[0]',
            'check': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:9876543 FS, negedge SE:3456789 FS,0.2000 : 200000FS, -0.0100 : -10000 FS )'
        },
        {
            'hier': 'top.cpu.core.reg_file.data_reg[0]',
            'check': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:5555555 FS, negedge SE:4444444 FS,0.0999 : 99900FS, -0.0333 : -33300 FS )'
        },
        {
            'hier': 'top.cpu.core.reg_file.data_reg[0]',
            'check': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:8888888 FS, negedge SE:2222222 FS,0.1800 : 180000FS, -0.0150 : -15000 FS )'
        }
    ]
    
    # 演示标准化过程
    def normalize_check_info(check_info):
        """简化的标准化函数"""
        try:
            start_idx = check_info.find('(')
            end_idx = check_info.rfind(')')
            
            if start_idx == -1 or end_idx == -1:
                return check_info
            
            prefix = check_info[:start_idx + 1]
            bracket_content = check_info[start_idx + 1:end_idx]
            parts = bracket_content.split(',')
            
            if len(parts) < 3:
                return check_info
            
            normalized_parts = []
            
            # 处理第一部分
            part1 = parts[0].strip()
            colon_idx = part1.find(':')
            if colon_idx != -1:
                part1 = part1[:colon_idx].strip()
            normalized_parts.append(part1)
            
            # 处理第二部分
            part2 = parts[1].strip()
            colon_idx = part2.find(':')
            if colon_idx != -1:
                part2 = part2[:colon_idx].strip()
            normalized_parts.append(part2)
            
            # 重新组装
            normalized_bracket_content = ', '.join(normalized_parts)
            return prefix + normalized_bracket_content + ')'
            
        except Exception:
            return check_info
    
    # 标准化历史模式
    historical_normalized = normalize_check_info(first_violation['check'])
    print(f"历史模式标准化: {historical_normalized}")
    print()
    
    # 检查后续违例是否可以自动确认
    for i, violation in enumerate(subsequent_violations, 1):
        print(f"违例 {i}:")
        print(f"  检查信息: {violation['check']}")
        
        # 标准化当前违例
        current_normalized = normalize_check_info(violation['check'])
        print(f"  标准化后: {current_normalized}")
        
        # 检查是否匹配
        is_match = historical_normalized == current_normalized
        
        if is_match:
            print(f"  ✓ 模糊匹配成功！")
            print(f"  → 自动应用历史确认:")
            print(f"    确认人: {first_violation['confirmer']}")
            print(f"    确认结果: {first_violation['result']}")
            print(f"    确认理由: {first_violation['reason']}")
        else:
            print(f"  ✗ 匹配失败，需要手动确认")
        
        print()
    
    # 演示不匹配的情况
    print("场景3：不同类型的违例（应该不匹配）")
    print("-" * 50)
    
    different_violations = [
        {
            'type': 'hold检查',
            'check': 'setuphold<hold>( negedge cpN && nCD nD SI SDFCHK:1234567 FS, negedge SE:7654321 FS,0.1500 : 150000FS, -0.0200 : -20000 FS )'
        },
        {
            'type': 'width检查',
            'check': 'width( signal:12345 FS, min_width, extra_info )'
        },
        {
            'type': '不同层级',
            'check': 'setuphold<setup>( negedge cpN && nCD nD SI SDFCHK:1234567 FS, negedge SE:7654321 FS,0.1500 : 150000FS, -0.0200 : -20000 FS )',
            'hier': 'top.cpu.core.reg_file.addr_reg[0]'  # 不同的层级路径
        }
    ]
    
    for violation in different_violations:
        print(f"{violation['type']}:")
        print(f"  检查信息: {violation['check']}")
        
        # 检查层级路径
        if 'hier' in violation:
            hier_match = violation['hier'] == first_violation['hier']
            print(f"  层级匹配: {'✓' if hier_match else '✗'}")
            if not hier_match:
                print(f"  → 层级路径不同，不会匹配")
                print()
                continue
        
        # 标准化检查信息
        current_normalized = normalize_check_info(violation['check'])
        print(f"  标准化后: {current_normalized}")
        
        # 检查是否匹配
        is_match = historical_normalized == current_normalized
        
        if is_match:
            print(f"  ✗ 意外匹配了！这可能是个问题")
        else:
            print(f"  ✓ 正确地没有匹配")
        
        print()
    
    print("=" * 80)
    print("使用示例总结:")
    print("1. 首次遇到违例时，工程师手动确认并保存模式")
    print("2. 后续相同类型但时间戳不同的违例可以自动确认")
    print("3. 不同类型或不同层级的违例不会误匹配")
    print("4. 大大减少了重复的手动确认工作")
    print("=" * 80)

def show_integration_workflow():
    """展示与现有工作流程的集成"""
    
    print("\n" + "=" * 80)
    print("与现有工作流程的集成")
    print("=" * 80)
    print()
    
    workflow_steps = [
        "1. 工程师选择vio_summary.log文件",
        "2. 插件解析时序违例条目",
        "3. 点击'应用历史确认'按钮",
        "4. 系统自动进行模糊匹配:",
        "   - 首先尝试精确匹配",
        "   - 精确匹配失败时尝试模糊匹配",
        "   - 应用匹配成功的历史确认记录",
        "5. 对于无法自动确认的违例，工程师手动确认",
        "6. 手动确认时，系统提供基于模糊匹配的建议",
        "7. 新的确认记录自动保存为历史模式"
    ]
    
    for step in workflow_steps:
        print(step)
    
    print()
    print("优势:")
    print("- 无需修改现有工作流程")
    print("- 自动化程度更高")
    print("- 减少重复性工作")
    print("- 保持确认的准确性")
    print("=" * 80)

if __name__ == "__main__":
    simulate_usage_scenario()
    show_integration_workflow()
